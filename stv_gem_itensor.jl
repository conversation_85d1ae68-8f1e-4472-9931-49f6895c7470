using ITensors
using ITensorInfiniteMPS
using TensorOperations
using Random

# --- Step 1: Construct the MPO tensor as specified ---
ϵ = 0.1
δ = 0.05
copy3 = [a == b == c ? 1 : 0 for a in 1:2, b in 1:2, c in 1:2]
Rstvs = zeros(2, 2, 2)
Rstvs[1,1,1] = Rstvs[1,2,1] = Rstvs[1,1,2] = 1
Rstvs[2,2,2] = 1

N = [1-ϵ δ; ϵ 1-δ];

NRstvs = ncon([N, Rstvs], [[-1,1], [1,-2,-3]]);
MPO_tensor_user = ncon([copy3, NRstvs], [[-1,1,-4], [-2,1,-3]]);

println("Size of the constructed 4-index tensor: ", size(MPO_tensor_user))
println("-"^50)

# --- Step 2: Convert the Julia Array to an ITensor MPO ---
MPO_tensor_std = permutedims(MPO_tensor_user, (1, 3, 2, 4));

phys_dim = size(MPO_tensor_std, 3)
virt_dim = size(MPO_tensor_std, 1)

s = Index(phys_dim, "Site")
l = Index(virt_dim, "Link,left")
r = Index(virt_dim, "Link,right")

W_itensor = -ITensor(MPO_tensor_std, l, r, s', s)
W = MPO([W_itensor]);

# --- Step 3: Initialize a random Infinite MPS as a starting guess ---
Random.seed!(1234)
sites = [s]

# *** THIS IS THE CORRECTED LINE ***
# The VUMPS solver requires the initial state to be of type `InfiniteCanonicalMPS`.
ψ₀ = InfiniteCanonicalMPS(sites)

# --- Step 4: Run VUMPS to find the fixed-point MPS ---
println("Starting VUMPS to find the fixed-point MPS...")

vumps_kwargs = (tol=1E-10, maxiter=100, outputlevel=1)
energy, ψ = vumps(W, ψ₀; vumps_kwargs...)
dominant_eigenvalue = -energy

println("-"^50)
println("VUMPS algorithm has converged.")
println("The dominant eigenvalue (λ) of the MPO is: ", dominant_eigenvalue)
println("The bond dimension of the fixed-point MPS is: ", linkdim(ψ, 1))
println("\nFixed-point MPS tensor for the unit cell (ψ.A[1]):")
print(ψ.A[1])