"""
Stavskaya Cellular Automaton Fixed-Point MPS Analysis

This script analyzes the fixed-point Matrix Product States (MPS) for the Stavskaya
cellular automaton using the VUMPS (Variational Uniform Matrix Product States) algorithm.

The Stavskaya cellular automaton is a probabilistic cellular automaton with:
- Binary states {0, 1} per site
- Majority rule with noise parameters ε and δ
- Asymmetric noise that breaks up-down symmetry

Author: [Your Name]
"""

using TensorOperations, Revise, LinearAlgebra, Dates
includet("canonical.jl")

# =============================================================================
# GLOBAL PARAMETERS AND CONSTANTS
# =============================================================================

# Algorithm parameters
const χ = 26                    # Bond dimension for MPS
const d = 2                     # Physical dimension (binary states)
const tol = 1e-10              # Convergence tolerance
const max_iter = 500           # Maximum iterations

# Pauli matrices for observables
const Z = [1 0; 0 -1]          # Pauli-Z (magnetization)
const X = [0 1; 1 0]           # Pauli-X (flip operator)

# Physical state vector (uniform superposition)
const xp = [1, 1]

# Rule tensors for Stavskaya cellular automaton
const copy3 = [a == b == c ? 1 : 0 for a in 1:2, b in 1:2, c in 1:2]

# Stavskaya rule tensor (majority rule with ties broken up)
const Rstvs = let
    R = zeros(2, 2, 2)
    R[1,1,1] = R[1,2,1] = R[1,1,2] = 1  # 0 wins if majority or tie
    R[2,2,2] = 1                         # 1 wins only if unanimous
    R
end

# Inverted Stavskaya rule tensor (ties broken down)
const R_dn = let
    R = zeros(2, 2, 2)
    R[2,2,2] = R[2,1,2] = R[2,2,1] = 1  # 1 wins if majority or tie
    R[1,1,1] = 1                         # 0 wins only if unanimous
    R
end

# =============================================================================
# CORE FUNCTIONS FOR MPS OPERATIONS
# =============================================================================

"""
    applyMPO(MPO, A0) -> Array{3}

Apply a Matrix Product Operator (MPO) to an MPS tensor A0.

# Arguments
- `MPO`: 4-tensor with legs (left_virtual, physical_out, right_virtual, physical_in)
- `A0`: 3-tensor MPS with legs (left_bond, physical, right_bond)

# Returns
- Modified MPS tensor with increased bond dimension
"""
function applyMPO(MPO, A0) where T
    D, d = size(A0, 1), size(A0, 2)
    χ0 = size(MPO, 1)

    # Contract MPO with MPS: MPO[vL,s_out,vR,s_in] * A0[αL,s_in,αR]
    A = ncon([MPO, A0], [[-2,1,-5,-3], [-1,1,-4]])

    # Reshape to proper MPS form: (D*χ0, d, D*χ0)
    return reshape(A, D*χ0, d, D*χ0)
end

"""
    observable(O, AL) -> Number

Compute the expectation value of operator O in the uniform MPS state AL.

# Arguments
- `O`: 2×2 operator matrix
- `AL`: Left-canonical MPS tensor

# Returns
- Expectation value ⟨ψ|O|ψ⟩
"""
function observable(O, AL) where T
    # Build transfer matrix by contracting physical indices
    B = ncon([AL, xp], [[-1,1,-2], [1]])

    # Find right and left eigenvectors of transfer matrix
    λ2s, ρs, info = eigsolve(B, 1, :LM; ishermitian=false)
    rB = ρs[1]

    λ2s, ρs, info = eigsolve(transpose(B), 1, :LM; ishermitian=false)
    lB = ρs[1]

    # Compute expectation value
    numerator = ncon([lB, AL, rB, O, xp], [[1], [1,3,2], [2], [3,4], [4]])
    denominator = transpose(lB) * B * rB

    return numerator / denominator
end

"""
    truncateMPO(AL, χ) -> Tuple{Array{3}, Diagonal, Array{3}}

Truncate MPS to maximum bond dimension χ using SVD compression.

# Arguments
- `AL`: MPS tensor to truncate
- `χ`: Maximum bond dimension

# Returns
- `(AL_new, C, AR_new)`: Truncated left-canonical, center matrix, right-canonical tensors
"""
function truncateMPO(AL, χ::Int) where T
    # Convert to mixed canonical form
    AL, AR, C = mixcanonical(AL)

    # SVD of center matrix
    F = svd(C)

    # Determine truncation dimension
    χ_trunc = min(χ, count(s -> s > 1e-12, F.S))

    # Truncate and reconstruct
    C_new = Diagonal(F.S[1:χ_trunc])
    U = F.U[:, 1:χ_trunc]
    Vt = F.Vt[1:χ_trunc, :]

    # Transform MPS tensors
    AL_new = ncon([U', AL, U], [[-1,1], [1,-2,3], [3,-3]])
    AR_new = ncon([Vt, AR, Vt'], [[-1,1], [1,-2,3], [3,-3]])

    return AL_new, C_new, AR_new
end


"""
    itebd(T; initial=[0., 1.], verbose=false) -> Tuple{Array{3}, Diagonal, Array{3}}

Imaginary Time Evolution Block Decimation (iTEBD) algorithm to find the fixed-point MPS.

This function iteratively applies the transfer matrix T to find the dominant eigenstate,
which corresponds to the stationary state of the cellular automaton.

# Arguments
- `T`: Transfer matrix (4-tensor) representing the cellular automaton dynamics
- `initial`: Initial physical state vector (default: [0., 1.])
- `verbose`: Whether to print convergence information

# Returns
- `(AL, C, AR)`: Left-canonical, center matrix, and right-canonical MPS tensors

# Algorithm
1. Initialize MPS from product state
2. Iteratively apply transfer matrix
3. Truncate to maintain bond dimension
4. Monitor convergence via magnetization observable
"""
function itebd(T; initial=[0., 1.], verbose::Bool=false)
    # Initialize MPS as product state
    AL = zeros(1, 2, 1)
    AL[1, :, 1] = initial
    AL, C, AR = mixcanonical(AL)

    # Convergence monitoring
    iter = 0
    err = 1.0
    obZ_old = 10.0  # Initial value for magnetization

    while err > tol && iter < max_iter
        # Apply transfer matrix (time evolution step)
        AL = applyMPO(T, AL)

        # Truncate to maintain bond dimension
        AL, C, AR = truncateMPO(AL, χ)

        # Compute entanglement entropy for monitoring
        S2 = diag(C).^2
        SvN = -sum(s -> s > 1e-16 ? s * log(s) : 0.0, S2)  # Avoid log(0)

        # Monitor convergence via magnetization
        obZ = observable(Z, AL)
        err = abs(obZ - obZ_old)
        obZ_old = obZ

        # Clean up small imaginary parts
        if abs(imag(obZ)) < 1e-10
            obZ = real(obZ)
        end

        if verbose
            println("iter = $iter, bond_dim = $(size(C,1)), SvN = $(round(SvN, digits=6)), " *
                   "⟨Z⟩ = $(round(real(obZ), digits=6)), err = $(round(err, digits=8))")
        end

        iter += 1
    end

    if iter >= max_iter
        @warn "iTEBD did not converge after $max_iter iterations (err = $err)"
    elseif verbose
        println("✓ Converged after $iter iterations")
    end

    return AL, C, AR
end

# =============================================================================
# ANALYSIS AND UTILITY FUNCTIONS
# =============================================================================

"""
    get_B_lr(A) -> Tuple{Matrix, Vector, Vector}

Compute the transfer matrix and its left/right eigenvectors for MPS tensor A.

# Returns
- `(B, lB, rB)`: Transfer matrix, left eigenvector, right eigenvector
"""
function get_B_lr(A::Array{T,3}) where T
    # Build transfer matrix by contracting physical indices
    B = ncon([A, xp], [[-1,1,-2], [1]])

    # Find dominant right eigenvector
    λ2s, ρs, info = eigsolve(B, 1, :LM; ishermitian=false)
    rB = ρs[1]

    # Find dominant left eigenvector
    λ2s, ρs, info = eigsolve(transpose(B), 1, :LM; ishermitian=false)
    lB = ρs[1]

    return B, lB, rB
end

"""
    get_RDM(A) -> Matrix

Compute the reduced density matrix (single-site density matrix) for uniform MPS A.

# Arguments
- `A`: MPS tensor

# Returns
- 2×2 reduced density matrix ρ = Tr_{all but one site}|ψ⟩⟨ψ|
"""
function get_RDM(A::Array{T,3}) where T
    B, lB, rB = get_B_lr(A)

    # Normalization factor
    norm = transpose(lB) * B * rB

    # Compute reduced density matrix
    RDM = ncon([lB, A, rB], [[1], [1,-1,2], [2]]) / norm

    return RDM
end

"""
    get_Tinf(T) -> Number

Compute the dominant eigenvalue of transfer matrix T.

# Arguments
- `T`: Transfer matrix

# Returns
- Dominant eigenvalue (should be 1 for properly normalized stochastic systems)
"""
function get_Tinf(T::Matrix{S}) where S
    # Right eigenvector
    λ2s, ρs, info = eigsolve(T, 1, :LM; ishermitian=false)
    rT = ρs[1]

    # Left eigenvector
    λ2s, ρs, info = eigsolve(transpose(T), 1, :LM; ishermitian=false)
    lT = ρs[1]

    # Compute eigenvalue
    Tinf = transpose(lT) * T * rT

    return Tinf
end

"""
    get_fidelity2(A1, A2) -> Number

Compute the fidelity between two uniform MPS states A1 and A2.

The fidelity is defined as F = |⟨ψ₁|ψ₂⟩|² / (⟨ψ₁|ψ₁⟩⟨ψ₂|ψ₂⟩).

# Arguments
- `A1`, `A2`: MPS tensors representing the two states

# Returns
- Fidelity value (1 = identical states, 0 = orthogonal states)
"""
function get_fidelity2(A1::Array{T,3}, A2::Array{T,3}) where T
    D1 = size(A1, 1)
    D2 = size(A2, 1)

    # Cross transfer matrix ⟨ψ₁|ψ₂⟩
    T12 = ncon([A1, A2], [[-1,1,-3], [-2,1,-4]])
    T12 = reshape(T12, (D1*D2, D1*D2))
    Tinf_12 = get_Tinf(T12)

    # Self transfer matrix ⟨ψ₁|ψ₁⟩
    T11 = ncon([A1, A1], [[-1,1,-3], [-2,1,-4]])
    T11 = reshape(T11, (D1*D1, D1*D1))
    Tinf_11 = get_Tinf(T11)

    # Self transfer matrix ⟨ψ₂|ψ₂⟩
    T22 = ncon([A2, A2], [[-1,1,-3], [-2,1,-4]])
    T22 = reshape(T22, (D2*D2, D2*D2))
    Tinf_22 = get_Tinf(T22)

    # Compute fidelity
    fidelity2 = Tinf_12 / sqrt(Tinf_11 * Tinf_22)

    return fidelity2
end

# =============================================================================
# MAIN ANALYSIS: PARAMETER SWEEP
# =============================================================================

"""
Main analysis routine that sweeps over noise parameters ε and δ to study
the phase diagram of the Stavskaya cellular automaton.

For each parameter combination, we compute:
1. Fixed-point MPS for standard Stavskaya rule (ties broken up)
2. Fixed-point MPS for inverted Stavskaya rule (ties broken down)
3. Fidelity between the two fixed points (measures symmetry breaking)
"""

# Parameter ranges for phase diagram
const ϵlist = LinRange(0, 0.5, 6)  # Noise parameter
const δlist = LinRange(0, 0.5, 6)  # Asymmetry parameter

println("="^80)
println("STAVSKAYA CELLULAR AUTOMATON PHASE DIAGRAM ANALYSIS")
println("="^80)
println("Bond dimension χ = $χ")
println("Tolerance = $tol")
println("Parameter ranges: ε ∈ [0, 0.5], δ ∈ [0, 0.5]")
println("="^80)

# Results storage
results = []

for (i, ϵ) in enumerate(ϵlist), (j, δ) in enumerate(δlist)
    println("\n" * "─"^60)
    println("Parameter point ($i,$j): ε = $(round(ϵ, digits=3)), δ = $(round(δ, digits=3))")
    println("─"^60)

    # Build noise matrix
    N = [1-ϵ δ; ϵ 1-δ]

    # -------------------------------------------------------------------------
    # STANDARD STAVSKAYA RULE (ties broken up)
    # -------------------------------------------------------------------------
    println("Computing standard Stavskaya fixed point...")

    # Build MPO: contract noise with rule tensor, then with copy tensor
    NRstvs = ncon([N, Rstvs], [[-1,1], [1,-2,-3]])
    MPO = ncon([copy3, NRstvs], [[-1,1,-4], [-2,1,-3]])

    # Convert to transfer matrix format for iTEBD
    T = permutedims(MPO, (1, 4, 3, 2))

    # Find fixed-point MPS
    AL_up, C_up, AR_up = itebd(T; verbose=false)
    # RDM_up = get_RDM(AL_up)

    # println("  Standard rule RDM diagonal: [$(round(real(RDM_up[1,1]), digits=4)), $(round(real(RDM_up[2,2]), digits=4))]")

    # -------------------------------------------------------------------------
    # INVERTED STAVSKAYA RULE (ties broken down)
    # -------------------------------------------------------------------------
    println("Computing inverted Stavskaya fixed point...")

    # Build MPO with inverted rule
    NR_dn = ncon([N, R_dn], [[-1,1], [1,-2,-3]])
    MPO_dn = ncon([copy3, NR_dn], [[-1,1,-4], [-2,1,-3]])

    # Convert to transfer matrix format
    T_dn = permutedims(MPO_dn, (1, 4, 3, 2))

    # Find fixed-point MPS
    AL_dn, C_dn, AR_dn = itebd(T_dn; initial=[0., 1.], verbose=false)
    RDM_dn = get_RDM(AL_dn)

    println("  Inverted rule RDM diagonal: [$(round(real(RDM_dn[1,1]), digits=4)), $(round(real(RDM_dn[2,2]), digits=4))]")

    # -------------------------------------------------------------------------
    # SYMMETRY ANALYSIS
    # -------------------------------------------------------------------------

    # Compute fidelity between the two fixed points
    fidelity2 = get_fidelity2(AL_up, AL_dn)
    fidelity_magnitude = abs(fidelity2)

    println("  Fidelity |⟨ψ_up|ψ_down⟩|² = $(round(fidelity_magnitude, digits=6))")

    # Store results
    push!(results, (ε=ϵ, δ=δ, RDM_up=RDM_up, RDM_dn=RDM_dn, fidelity=fidelity_magnitude))

    # Interpretation
    if fidelity_magnitude > 0.99
        println("  → High symmetry phase (rules give same fixed point)")
    elseif fidelity_magnitude < 0.01
        println("  → Broken symmetry phase (rules give orthogonal fixed points)")
    else
        println("  → Intermediate regime")
    end
end

println("\n" * "="^80)
println("ANALYSIS COMPLETE")
println("="^80)

# =============================================================================
# POST-PROCESSING AND SUMMARY
# =============================================================================

"""
    summarize_results(results)

Print a summary of the phase diagram analysis.
"""
function summarize_results(results)
    println("\nPHASE DIAGRAM SUMMARY:")
    println("─"^40)

    high_sym_count = count(r -> r.fidelity > 0.99, results)
    broken_sym_count = count(r -> r.fidelity < 0.01, results)
    intermediate_count = length(results) - high_sym_count - broken_sym_count

    println("High symmetry points (F > 0.99): $high_sym_count")
    println("Broken symmetry points (F < 0.01): $broken_sym_count")
    println("Intermediate points: $intermediate_count")
    println("Total points analyzed: $(length(results))")

    # Find most asymmetric point
    min_fidelity_idx = argmin([r.fidelity for r in results])
    max_asym = results[min_fidelity_idx]
    println("\nMost asymmetric point:")
    println("  ε = $(round(max_asym.ε, digits=3)), δ = $(round(max_asym.δ, digits=3))")
    println("  Fidelity = $(round(max_asym.fidelity, digits=6))")

    return results
end

# Generate summary
final_results = summarize_results(results)

# Optional: Save results to file
# using JLD2
# @save "stavskaya_results.jld2" final_results ϵlist δlist χ tol

println("\n✓ All computations completed successfully!")
println("Results stored in 'results' variable for further analysis.")