using TensorOperations
using TensorKit
using MPSKit
using KrylovKit

# ── 1) Build your MPO (legs: vL, ket, vR, bra) using @tensor instead of ncon ──
ϵ = 0.1
δ = 0.05

copy3 = [a == b == c ? 1.0 : 0.0 for a in 1:2, b in 1:2, c in 1:2] # Float
Rstvs = zeros(Float64, 2, 2, 2)
Rstvs[1,1,1] = 1
Rstvs[1,2,1] = 1
Rstvs[1,1,2] = 1
Rstvs[2,2,2] = 1

N = Float64.([1-ϵ  δ;  ϵ  1-δ])   # ← trailing dot causes Float64.@tensor on next line
   # 2×2

# NRstvs[a,b,c] = Σ_x N[a,x] * Rstvs[x,b,c]
@tensor NRstvs[a,b,c] := N[a,x] * Rstvs[x,b,c]

# MPO[a,b,c,d] = Σ_x copy3[a,x,d] * NRstvs[b,x,c]
# (output legs: a=vL, b=ket, c=vR, d=bra)
@tensor MPO[a,b,c,d] := copy3[a,x,d] * NRstvs[b,x,c]

@assert size(MPO) == (2,2,2,2)

# ── 2) Wrap as a TensorMap for a uniform MPO: W : (vL ⊗ ket) → (vR ⊗ bra) ──
d  = 2
Dv = 2
p  = ℂ^d
v  = ℂ^Dv

# Permute (vL, ket, vR, bra) → (vR, bra, vL, ket), then make linear map cod ← dom
MPOmat = permutedims(MPO, (3,4,1,2))                # (vR, bra, vL, ket)
MPOmat = reshape(MPOmat, Dv*d, Dv*d)                # matrix

W = TensorMap(MPOmat, (v ⊗ p) ← (v ⊗ p))
# Umpo = UniformMPO(W)
Umpo = InfiniteMPO(W)

# ── 3) Power iterate on the uniform MPS manifold to get dominant fixed point ──
χ = 4
ψ = random_mps(UniformMPS, p; bond_dim=χ)

maxiter = 200
tol = 1e-10
λ_old = 0.0

for it in 1:maxiter
    ψ = apply(Umpo, ψ; maxiter=20, tol=1e-12)   # apply MPO
    ψ = normalize(ψ)
    ψ = truncate(ψ; maxdim=χ, cutoff=1e-12)

    num = inner(ψ, apply(Umpo, ψ))
    den = inner(ψ, ψ)
    λ = num/den

    if abs(λ - λ_old) < tol
        @info "Converged after $it steps. λ ≈ $(real(λ))"
        break
    end
    λ_old = λ
end

# ── 4) (Optional) Inspect one-site reduced operator (vectorized density) ──
ρ1 = one_site_reduced_density_matrix(ψ)  # on p ⊗ p̄ (a 4×4)
println(Matrix(ρ1))
