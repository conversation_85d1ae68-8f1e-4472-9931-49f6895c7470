using TensorOperations, Revise
includet("vumpsfixedpts.jl")


χ = 26; d = 2; tol = 1e-10; max_iter = 500
Z = [1 0; 0 -1]; X = [0 1; 1 0]
xp = [1,1]
copy3 = [a == b == c ? 1 : 0 for a in 1:2, b in 1:2, c in 1:2]
Rstvs = zeros(2, 2, 2)
Rstvs[1,1,1] = Rstvs[1,2,1] = Rstvs[1,1,2] = 1
Rstvs[2,2,2] = 1
R_dn = zeros(2, 2, 2)
R_dn[2,2,2] = R_dn[2,1,2] = R_dn[2,2,1] = 1
R_dn[1,1,1] = 1
xp = [1,1]





##

function applyMPO(MPO, A0)
    D,d, = size(A0)
    χ0 = size(MPO)[1]
    A = ncon([MPO, A0], [[-2,1,-5,-3], [-1,1,-4]])
    A = reshape(A, D*χ0, d, D*χ0)
end

function observable(O,AL)
    B = ncon([AL,xp], [[-1,1,-2],[1]])
    λ2s, ρs, info = eigsolve(B, 1, :LM, ishermitian = false)
    rB = ρs[1]
    λ2s, ρs, info = eigsolve(transpose(B), 1, :LM, ishermitian = false)
    lB = ρs[1]
    numerator = ncon([lB, AL, rB,O,xp], [[1],[1,3,2],[2],[3,4],[4]])
    denominator = transpose(lB)*B*rB
    return numerator/denominator
end

function truncateMPO(AL,χ)
    AL, AR, C = mixcanonical(AL)
    F = svd(C)
    χ_trun = min(χ, length(F.S[ (F.S) .> 1e-12]))
    C = Diagonal(F.S[1:χ_trun])
    U = F.U[:,1:χ_trun]; Vt = F.Vt[1:χ_trun,:]
    AL = ncon([U', AL, U], [[-1,1],[1,-2,3],[3,-3]])
    AR = ncon([Vt, AR, Vt'], [[-1,1],[1,-2,3],[3,-3]])
    return AL, C, AR
end



function itebd(T; initial = [0., 1.], verbose = false)
    AL = zeros(1,2,1)
    AL[1,:,1] = initial
    AL, C, AR = mixcanonical(AL)
    iter = 0
    err = 1; obZ_old = 10
    while err > tol && iter < max_iter
        AL = applyMPO(T,AL)
        AL, C, AR = truncateMPO(AL,χ)
        S2 = diag(C).^2
        SvN = -sum(S2 .* log.(S2) ); 
        SvN_old = SvN
        obZ = observable(Z,AL)
        err = abs(obZ - obZ_old)
        obZ_old = obZ
        if imag(obZ) < 1e-10
            obZ = real(obZ)
        end

        verbose && println("iter = $iter, size(C) = $(size(C)), SvN = $SvN, obZ = $obZ, err = $err")
        iter +=1
    end
    # B, lB, rB = get_B_lr(AL)
    # norm = transpose(lB)*B*rB
    # AL = AL/norm
    return AL, C, AR 
end

function get_B_lr(A)
    B = ncon([A,xp], [[-1,1,-2],[1]])
    λ2s, ρs, info = eigsolve(B, 1, :LM, ishermitian = false)
    rB = ρs[1]
    λ2s, ρs, info = eigsolve(transpose(B), 1, :LM, ishermitian = false)
    lB = ρs[1]
    return B, lB, rB
end

function get_RDM(A)
    B, lB, rB = get_B_lr(A)
    norm = transpose(lB)*B*rB
    # @show size(B), size(lB), size(rB)
    RDM = ncon([lB, A, rB], [[1],[1,-1,2],[2]]) / norm; 
    return RDM
end


function get_Tinf(T)
    λ2s, ρs, info = eigsolve(T, 1, :LM, ishermitian = false)
    rT = ρs[1]
    λ2s, ρs, info = eigsolve(transpose(T), 1, :LM, ishermitian = false)
    lT = ρs[1]
    Tinf =  transpose(lT)*T*rT
    # @show Tinf
    return Tinf
end

function get_fidelity2(A1, A2)
    D1, = size(A1)
    D2, = size(A2)
    T12 = ncon([A1, A2], [[-1,1,-3], [-2,1,-4]])
    T12 = reshape(T12, (D1*D2, D1*D2))
    Tinf_12 = get_Tinf(T12)

    T11 = ncon([A1, A1], [[-1,1,-3], [-2,1,-4]])
    T11 = reshape(T11, (D1*D1, D1*D1))
    Tinf_11 = get_Tinf(T11)

    T22 = ncon([A2, A2], [[-1,1,-3], [-2,1,-4]])
    T22 = reshape(T22, (D2^2, D2^2))
    Tinf_22 = get_Tinf(T22)

    fidelity2 = Tinf_12/sqrt(Tinf_11*Tinf_22)
    return fidelity2
end

ϵlist = LinRange(0,0.5,6)
δlist = LinRange(0,0.5,6)

# ϵ = 0.1
# δ = 0.1

for ϵ in ϵlist, δ in δlist
    println("-"^50)
    @show ϵ, δ
    N = [1-ϵ δ; ϵ 1-δ];

    NRstvs = ncon([N, Rstvs], [[-1,1], [1,-2,-3]]);
    MPO = ncon([copy3, NRstvs], [[-1,1,-4], [-2,1,-3]]);

    T = permutedims(MPO, (1, 4, 3, 2));

    AL_up, C_up, AR_up  = itebd(T);
    RDM =  get_RDM(AL_up); 
    RDM = round.(RDM, digits = 8)
    # display(RDM)
    # display(RDM)
    ##




    NR_dn = ncon([N, R_dn], [[-1,1], [1,-2,-3]]);
    MPO_dn = ncon([copy3, NR_dn], [[-1,1,-4], [-2,1,-3]]);

    T_dn = permutedims(MPO_dn, (1, 4, 3, 2));

    AL_dn, C_dn, AR_dn  = itebd(T_dn; initial = [0. , 1.]);
    RDM =  get_RDM(AL_dn); 
    RDM = round.(RDM, digits = 8)
    # display(RDM)

    # sum(AL)

    ##
    fidelity2 = get_fidelity2(AL_up, AL_dn); @show norm(fidelity2);
    # fidelity2 = get_fidelity2(AL_dn, AL_up); @show norm(fidelity2);

end