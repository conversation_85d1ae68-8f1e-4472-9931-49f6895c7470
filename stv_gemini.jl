# 1. Package Imports
using MPSKit
using TensorKit
using TensorOperations

# Make output cleaner
# Define a compact show method for TensorMap to avoid flooding the console
function preview_tensor(io::IO, t::TensorMap)
    println(io, "TensorMap(...")
    for (key, val) in blocks(t)
        println(io, "  $key => $(size(val)) matrix")
    end
    print(io, "...)")
end
Base.show(io::IO, t::TensorMap) = preview_tensor(io, t)


# 2. MPO Tensor Construction (as provided in the question)
ϵ = 0.6
δ = 0.0
# Note: Data type should be Float64 for compatibility with ComplexSpace
copy3 = Float64[a == b == c ? 1 : 0 for a in 1:2, b in 1:2, c in 1:2]
Rstvs = zeros(Float64, 2, 2, 2)
Rstvs[1,1,1] = Rstvs[1,2,1] = Rstvs[1,1,2] = 1
Rstvs[2,2,2] = 1

N = Float64[1-ϵ δ; ϵ 1-δ];

NRstvs = ncon([N, Rstvs], [[-1,1], [1,-2,-3]]);

# The ncon contraction results in an MPO with indices:
# (virtual_left, physical_ket, virtual_right, physical_bra)
MPO_array = ncon([copy3, NRstvs], [[-1,1,-4], [-2,1,-3]]);
println("Size of the initial MPO array: ", size(MPO_array))

# 3. Index Permutation for MPSKit
# MPSKit expects the order: (virtual_left, virtual_right, physical_ket, physical_bra)
# We permute the 2nd and 3rd indices to match this convention.
MPO_permuted = permutedims(MPO_array, (1, 3, 2, 4));
println("Size of the permuted MPO array: ", size(MPO_permuted))
println("------------------------------------")

# 4. TensorMap Creation
# Define the physical and virtual spaces (both are 2-dimensional, i.e., qubits)
phys_space = ComplexSpace(2)
virt_space = ComplexSpace(2)

# Convert the Julia Array into a TensorKit.TensorMap
mpo_tensor = TensorMap(MPO_permuted, virt_space ⊗ virt_space ← phys_space ⊗ phys_space)

# 5. InfiniteMPO
mpo = InfiniteMPO([mpo_tensor])

# 6. Initial MPS
initial_mps = InfiniteMPS([phys_space], [virt_space]);

# 7. Finding the Fixed Point
fp_mps, env, λ = leading_boundary(initial_mps, mpo, VUMPS());

# 8. Results
println("Successfully found the fixed point.")
println("Dominant Eigenvalue (λ): ", λ[1])
println("\nFixed-point MPS tensor (A_L):")
println(fp_mps.AL[1])
println("------------------------------------")

# 9. Calculate Magnetization
println("Calculating magnetization...")

# Define the building blocks for the σ^z observable MPO
sigma_z_matrix = ComplexF64[1 0; 0 -1]
id_matrix = ComplexF64[1 0; 0 1]
mpo_virt_space = ComplexSpace(2) # The virtual space of the observable MPO

# Create the 4-index MPO tensor for the observable H = Σ σ^z_i
# The structure is [[I, 0], [σ^z, I]]
# Index order must be (v_left, v_right, p_ket, p_bra) for MPSKit
mpo_op_array = zeros(ComplexF64, 2, 2, 2, 2)
mpo_op_array[1, 1, :, :] = id_matrix
mpo_op_array[2, 1, :, :] = sigma_z_matrix
mpo_op_array[2, 2, :, :] = id_matrix

# Convert the Julia array to a TensorMap.
# There is a constructor for InfiniteMPOHamiltonian that takes the MPO tensor directly.
# This is the most fundamental and robust way.
ham_tensor = TensorMap(mpo_op_array, mpo_virt_space ⊗ mpo_virt_space ← phys_space ⊗ phys_space)
sigma_z_hamiltonian = InfiniteMPOHamiltonian(ham_tensor)

# Calculate the expectation value density (i.e., value per site)
magnetization = expectation_value(fp_mps, sigma_z_hamiltonian)

# Print the result
println("Magnetization <σ^z>: ", real(magnetization))