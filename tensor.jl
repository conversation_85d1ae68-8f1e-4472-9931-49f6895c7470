using TensorOperations
ϵ = 0.1
δ = 0.05
copy3 = [a == b == c ? 1 : 0 for a in 1:2, b in 1:2, c in 1:2]
Rstvs = zeros(2, 2, 2)
Rstvs[1,1,1] = Rstvs[1,2,1] = Rstvs[1,1,2] = 1
Rstvs[2,2,2] = 1

N = [1-ϵ δ; ϵ 1-δ];

NRstvs = ncon([N, Rstvs], [[-1,1], [1,-2,-3]]);
MPO = ncon([copy3, NRstvs], [[-1,1,-4], [-2,1,-3]]);
##



α = randn()
A = randn(5, 5, 5, 5, 5, 5)
B = randn(5, 5, 5)
C = randn(5, 5, 5)
D = zeros(5, 5, 5)
@tensor begin
    D[a, b, c] = A[a, e, f, c, f, g] * B[g, b, e] + α * C[c, a, b]
    E[a, b, c] := A[a, e, f, c, f, g] * B[g, b, e] + α * C[c, a, b]
end