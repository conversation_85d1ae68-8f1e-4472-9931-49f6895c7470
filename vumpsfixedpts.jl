using Revise
Revise.includet("canonical.jl")
##
#################### leftenv and rightenv
"""
    leftenv(A, M, FL; kwargs)
Compute the left environment tensor for MPS A and MPO M, by finding the left fixed point
of A - M - conj(A) contracted along the physical dimension.
"""
function leftenv(A::Dims3Array, M::Dims4Array, FL = randn(eltype(A), size(A,1), size(M,1), size(A,1)); kwargs...)
    λs, FLs, info = eigsolve(FL, 1, :LM; ishermitian = false, kwargs...) do FL
        FL = ncon([FL,A,M,conj(A)], [[1,2,3], [3,5,-3], [2,5,-2,4], [1,4,-1]])
    end
    return FLs[1], real(λs[1]), info
end
"""
    rightenv(A, M, FR; kwargs...)

Compute the right environment tensor for MPS A and MPO M, by finding the right fixed point
of A - M - conj(A) contracted along the physical dimension.
"""
function rightenv(A::Dims3Array, M::Dims4Array, FR = randn(eltype(A), size(A,1), size(M,1), size(A,1)); kwargs...)
    λs, FRs, info = eigsolve(FR, 1, :LM; ishermitian = false, kwargs...) do FR
        # @tensor FR[α,a,β] := A[α,s',α']*FR[α',a',β']*M[a,s,a',s']*conj(A[β,s,β'])
        FR = ncon([FR,A,M,conj(A)], [[1,2,3],[-1,4,1],[-2,4,2,5],[-3,5,3]])
    end
    return FRs[1], real(λs[1]), info
end



#################### Final algorithm
function vumpsfixedpts(A::Dims3Array, M::Dims4Array; verbose = true, steps = 100, tol = 1e-6, kwargs...)
    AL, AR, C = mixcanonical(A)
    FL, λL = leftenv(AL, M; kwargs...)
    FR, λR = rightenv(AR, M; kwargs...)
    normalization = ncon([FL, C, conj(C),FR], [[4,3,1],[1,2],[4,5],[2,3,5]])[1]
    FR ./=  normalization # normalize FL and FR: not really necessary
    iter = 0 # vumps step
    err = 1; λ = 0 #initial value, not important 
    Z = [1 0; 0 -1]
    obZ_old = observer(Z,AL)

    while err > tol && iter < steps
        @tensor AC[a,s,b] := AL[a,s,b']*C[b',b]
        applyH1 = (AC, FL, FR, M) -> ncon([AC,FL,FR,M], [[1,5,2], [-1,3,1], [2,4,-3], [3,5,4,-2]])
        applyH0 = (C, FL, FR) -> ncon([C, FL, FR], [[1,2], [-1,3,1], [2,3,-2]])
        μ1s, ACs, info1 = eigsolve(x->applyH1(x, FL, FR, M), AC, 1, :LM; ishermitian = false, maxiter = 1, kwargs...)
        μ0s, Cs, info0 = eigsolve(x->applyH0(x, FL, FR), C, 1; ishermitian = false, maxiter = 1, kwargs...)
        λ = real(μ1s[1]/μ0s[1])
        AC = ACs[1]
        C = Cs[1]
        AL, AR, errL, errR = min_AC_C(AC,C)
        AL, C, = leftorth(AR, C; tol = tol/10, kwargs...) # regauge MPS: not really necessary
        FL, λL = leftenv(AL, M, FL; tol = tol/10, kwargs...)
        FR, λR = rightenv(AR, M, FR; tol = tol/10, kwargs...)
        normalization = ncon([FL, C, conj(C),FR], [[4,3,1],[1,2],[4,5],[2,3,5]])[1]
        FR ./= normalization # normalize FL and FR: not really necessary
        # Convergence measure: norm of the projection of the residual onto the tangent space
        # @tensor AC[a,s,b] := AL[a,s,b']*C[b',b]
        # MAC = applyH1(AC, FL, FR, M)
        # @tensor MAC[a,s,b] -= AL[a,s,b']*(conj(AL[a',s',b'])*MAC[a',s',b])
        # err = norm(MAC)
        obZ_current = observer(Z,AL)
        err = abs(obZ_current - obZ_old)
        obZ_old = obZ_current
        iter += 1
        verbose && println("Step $(iter): λ ≈ $λ ≈ $λL ≈ $λR, obZ = $(real(obZ_current)), err ≈ $err")
    end
    # F = svd(C)
    # C = Diagonal(F.S)
    # AL = ncon([F.U', AL, F.U], [[-1,1],[1,-2,3],[3,-3]])
    # AR = ncon([F.Vt, AR, F.Vt'], [[-1,1],[1,-2,3],[3,-3]])
    return λ, AL, C, AR, FL, FR, err
end

function observer(O,AL)
    # O = [1 0; 0 -1]
    B = ncon([AL,xp], [[-1,1,-2],[1]])
    λ2s, ρs, info = eigsolve(B, 1, :LM, ishermitian = false)
    rB = ρs[1]
    λ2s, ρs, info = eigsolve(transpose(B), 1, :LM, ishermitian = false)
    lB = ρs[1]
    numerator = ncon([lB, AL, rB,O,xp], [[1],[1,3,2],[2],[3,4],[4]])
    denominator = transpose(lB)*B*rB
    return numerator/denominator
end


